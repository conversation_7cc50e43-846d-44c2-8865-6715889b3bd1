{"parent": null, "pid": 28538, "argv": ["/Users/<USER>/.nvm/versions/node/v16.18.0/bin/node", "/Users/<USER>/triple-beam/node_modules/.bin/mocha", "test.js"], "execArgv": [], "cwd": "/Users/<USER>/triple-beam", "time": 1689003099183, "ppid": 28537, "coverageFilename": "/Users/<USER>/triple-beam/.nyc_output/c579bf8f-6820-47a5-b2da-a11267eb8435.json", "externalId": "", "uuid": "c579bf8f-6820-47a5-b2da-a11267eb8435", "files": ["/Users/<USER>/triple-beam/index.js", "/Users/<USER>/triple-beam/config/index.js", "/Users/<USER>/triple-beam/config/cli.js", "/Users/<USER>/triple-beam/config/npm.js", "/Users/<USER>/triple-beam/config/syslog.js"]}